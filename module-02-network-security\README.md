# 🌐 Module 2: Network Security

**Level**: Beginner-Intermediate  
**Duration**: 3-4 weeks  
**Prerequisites**: Module 1 completed, basic networking knowledge

## 📚 Learning Objectives

Setelah menyelesaikan modul ini, Anda akan mampu:
- ✅ Memahami network protocols dan security implications
- ✅ Mengkonfigurasi dan manage firewalls
- ✅ Melakukan network scanning dan reconnaissance
- ✅ Mengimplementasikan network segmentation
- ✅ Mendeteksi dan analyze network attacks
- ✅ Menggunakan IDS/IPS untuk network monitoring
- ✅ Mengamankan wireless networks
- ✅ Memahami VPN technologies dan implementation

## 📖 Course Content

### Week 1: Network Fundamentals & Protocols

#### 1.1 Network Security Fundamentals
- **OSI Model Security Implications**
  - Layer-specific threats dan protections
  - Protocol stack vulnerabilities
  - Defense in depth strategy

- **TCP/IP Protocol Suite Security**
  - IP vulnerabilities (spoofing, fragmentation)
  - TCP attacks (SYN flood, session hijacking)
  - UDP security considerations
  - ICMP security implications

#### 1.2 Network Protocols Deep Dive
- **Application Layer Protocols**
  - HTTP/HTTPS security
  - DNS security (DNS poisoning, DoH, DoT)
  - Email protocols (SMTP, POP3, IMAP) security
  - FTP/SFTP security considerations

- **Network Services Security**
  - DHCP security (rogue DHCP, starvation)
  - NTP security implications
  - SNMP security best practices
  - SSH hardening dan key management

### Week 2: Firewalls & Network Defense

#### 2.1 Firewall Technologies
- **Firewall Types**
  - Packet filtering firewalls
  - Stateful inspection firewalls
  - Application layer firewalls
  - Next-generation firewalls (NGFW)

- **Firewall Architectures**
  - Screened subnet (DMZ)
  - Dual-homed host
  - Screened host
  - Multiple firewall configurations

#### 2.2 Network Access Control
- **Network Segmentation**
  - VLANs dan security benefits
  - Micro-segmentation strategies
  - Zero trust network architecture
  - Software-defined perimeters

- **Access Control Methods**
  - 802.1X authentication
  - NAC (Network Access Control)
  - Port security
  - MAC address filtering

### Week 3: Network Monitoring & Detection

#### 3.1 Intrusion Detection Systems (IDS)
- **IDS Types**
  - Network-based IDS (NIDS)
  - Host-based IDS (HIDS)
  - Signature-based vs anomaly-based
  - Hybrid detection systems

- **IDS Implementation**
  - Sensor placement strategies
  - Signature management
  - False positive reduction
  - Alert correlation

#### 3.2 Network Traffic Analysis
- **Packet Capture & Analysis**
  - Wireshark advanced techniques
  - Network forensics
  - Traffic pattern analysis
  - Protocol analysis

- **Network Monitoring Tools**
  - SIEM integration
  - Flow analysis (NetFlow, sFlow)
  - Bandwidth monitoring
  - Performance correlation

### Week 4: Wireless Security & Advanced Topics

#### 4.1 Wireless Network Security
- **Wireless Protocols**
  - WEP vulnerabilities
  - WPA/WPA2 security
  - WPA3 improvements
  - Enterprise wireless security

- **Wireless Attacks**
  - War driving/walking
  - Evil twin attacks
  - WPS vulnerabilities
  - Bluetooth security

#### 4.2 VPN Technologies
- **VPN Types**
  - Site-to-site VPNs
  - Remote access VPNs
  - SSL/TLS VPNs
  - IPSec VPNs

- **VPN Security**
  - Encryption protocols
  - Authentication methods
  - Split tunneling risks
  - VPN vulnerabilities

## 🛠️ Hands-on Labs

### Lab 1: Firewall Configuration & Testing
**Objective**: Configure pfSense firewall dan test security rules

**Tasks**:
1. Install dan configure pfSense
2. Create firewall rules for different zones
3. Implement NAT dan port forwarding
4. Test firewall effectiveness
5. Configure logging dan monitoring

**Tools**: pfSense, Nmap, Metasploit

### Lab 2: Network Scanning & Reconnaissance
**Objective**: Perform comprehensive network reconnaissance

**Tasks**:
1. Network discovery dengan various techniques
2. Port scanning dan service enumeration
3. OS fingerprinting
4. Vulnerability scanning
5. Create network topology map

**Tools**: Nmap, Masscan, Zmap, Angry IP Scanner

### Lab 3: IDS/IPS Implementation
**Objective**: Deploy dan configure Suricata IDS/IPS

**Tasks**:
1. Install Suricata on network segment
2. Configure detection rules
3. Generate test traffic dan alerts
4. Analyze logs dan create reports
5. Tune system untuk reduce false positives

**Tools**: Suricata, ELK Stack, pfSense

### Lab 4: Wireless Security Assessment
**Objective**: Assess wireless network security

**Tasks**:
1. Wireless network discovery
2. WPA/WPA2 security testing
3. Rogue access point detection
4. Wireless packet capture dan analysis
5. Implement wireless security best practices

**Tools**: Aircrack-ng, Kismet, Wireshark

## 🎯 Project Challenge: Network Security Architecture

### Project Overview
Design dan implement comprehensive network security architecture untuk medium-sized organization (500 employees).

### Requirements:
- **Network Segmentation**: Design secure network zones
- **Firewall Strategy**: Multi-layer firewall implementation
- **Monitoring Solution**: IDS/IPS deployment plan
- **Wireless Security**: Secure wireless infrastructure
- **Remote Access**: VPN solution design
- **Incident Response**: Network security incident procedures

### Deliverables:

#### Phase 1: Network Design (Week 1)
- **Network Topology Diagram**
  - Physical dan logical network layout
  - Security zones identification
  - Trust boundaries definition
  - Critical asset placement

- **Security Requirements Analysis**
  - Compliance requirements (PCI DSS, HIPAA, etc.)
  - Business requirements
  - Risk assessment
  - Security controls mapping

#### Phase 2: Implementation Plan (Week 2)
- **Firewall Configuration**
  - Rule sets untuk each zone
  - NAT dan routing configuration
  - High availability setup
  - Management dan monitoring

- **Network Monitoring Strategy**
  - IDS/IPS placement
  - Log collection dan analysis
  - Alert correlation rules
  - Incident response integration

#### Phase 3: Testing & Validation (Week 3)
- **Security Testing**
  - Penetration testing plan
  - Vulnerability assessment
  - Configuration review
  - Performance testing

- **Documentation**
  - Network security policies
  - Operational procedures
  - Incident response playbooks
  - Training materials

### Assessment Criteria:
- **Technical Design** (30%): Architecture quality dan feasibility
- **Security Controls** (25%): Appropriate security measures
- **Implementation Plan** (20%): Realistic dan detailed planning
- **Documentation** (15%): Quality dan completeness
- **Innovation** (10%): Creative solutions dan best practices

## 📋 Assessment Methods

### Theory Assessment (35%)
- **Weekly Quizzes**: Protocol security, firewall concepts
- **Midterm Exam**: Network security fundamentals
- **Final Exam**: Comprehensive network security knowledge

### Practical Assessment (45%)
- **Lab Reports**: Detailed documentation of lab activities
- **Hands-on Skills**: Practical tool usage dan configuration
- **Troubleshooting**: Problem-solving capabilities

### Project Assessment (20%)
- **Network Security Architecture**: Comprehensive design project

## 🔧 Required Tools & Software

### Network Scanning:
- **Nmap** - Network discovery dan security auditing
- **Masscan** - High-speed port scanner
- **Zmap** - Internet-wide network scanner
- **Angry IP Scanner** - GUI-based network scanner

### Firewall & Security:
- **pfSense** - Open source firewall platform
- **OPNsense** - Alternative firewall solution
- **iptables** - Linux firewall management
- **Windows Firewall** - Built-in Windows protection

### Monitoring & Analysis:
- **Wireshark** - Network protocol analyzer
- **Suricata** - IDS/IPS engine
- **Snort** - Network intrusion detection
- **ELK Stack** - Log analysis platform

### Wireless Security:
- **Aircrack-ng** - Wireless security testing suite
- **Kismet** - Wireless network detector
- **Reaver** - WPS security testing
- **Wifite** - Automated wireless attack tool

### VPN & Remote Access:
- **OpenVPN** - Open source VPN solution
- **StrongSwan** - IPSec VPN implementation
- **WireGuard** - Modern VPN protocol

## 📚 Required Reading

### Books:
1. "Network Security Essentials" - William Stallings
2. "Firewalls and Internet Security" - Cheswick, Bellovin, Rubin
3. "Network Intrusion Detection" - Stephen Northcutt
4. "Wireless Security" - Bruce Potter

### Standards & RFCs:
- RFC 2828 - Internet Security Glossary
- RFC 4301 - Security Architecture for IP
- NIST SP 800-41 - Guidelines for Firewall Configuration
- NIST SP 800-97 - Wireless Network Security

### Online Resources:
- [SANS Network Security](https://www.sans.org/cyber-security-courses/network-security/)
- [Cisco Network Security](https://www.cisco.com/c/en/us/products/security/)
- [pfSense Documentation](https://docs.netgate.com/pfsense/)
- [Suricata User Guide](https://suricata.readthedocs.io/)

## 🎓 Certification Preparation

Modul ini mempersiapkan Anda untuk:
- **CompTIA Network+** (N10-008)
- **CompTIA Security+** (SY0-601) - Network Security domain
- **Cisco CCNA Security** (210-260)
- **SANS GIAC Security Essentials** (GSEC)

## ➡️ Next Steps

Setelah menyelesaikan Module 2, Anda siap untuk:
- [Module 3: Web Application Security](../module-03-web-security/)
- Advanced network security specializations
- Network security certifications

---

**🚀 Ready to secure networks? Begin with [Week 1: Network Fundamentals](./week-01/)**
