# 🛡️ Module 1: Cybersecurity Fundamentals

**Level**: Beginner  
**Duration**: 2-3 weeks  
**Prerequisites**: Basic computer knowledge

## 📚 Learning Objectives

Setelah menyelesaikan modul ini, Anda akan mampu:
- ✅ Memahami konsep dasar cybersecurity dan terminologi
- ✅ Menjelaskan CIA Triad dan prinsip keamanan informasi
- ✅ Mengidentifikasi berbagai jenis ancaman dan serangan siber
- ✅ Memahami risk management dan security frameworks
- ✅ Menggunakan tools dasar untuk security assessment
- ✅ Menerapkan best practices keamanan personal dan organisasi

## 📖 Course Content

### Week 1: Foundation Concepts

#### 1.1 Introduction to Cybersecurity
- **What is Cybersecurity?**
  - Definition dan scope cybersecurity
  - Mengapa cybersecurity penting di era digital
  - Career paths dalam cybersecurity
  - Industry statistics dan trends

- **CIA Triad - Core Principles**
  - **Confidentiality (Kerahasiaan)**
    - Data classification
    - Access controls
    - Encryption basics
  - **Integrity (Integritas)**
    - Data integrity
    - System integrity
    - Hash functions
  - **Availability (Ketersediaan)**
    - System uptime
    - Redundancy
    - Disaster recovery

#### 1.2 Threat Landscape
- **Types of Threats**
  - Natural disasters
  - Human threats (internal/external)
  - Technical failures
  - Environmental threats

- **Threat Actors**
  - Script kiddies
  - Cybercriminals
  - Hacktivists
  - Nation-state actors
  - Insider threats

- **Attack Vectors**
  - Social engineering
  - Malware
  - Network attacks
  - Physical attacks
  - Supply chain attacks

### Week 2: Security Frameworks & Risk Management

#### 2.1 Security Frameworks
- **NIST Cybersecurity Framework**
  - Identify
  - Protect
  - Detect
  - Respond
  - Recover

- **ISO 27001/27002**
  - Information Security Management System (ISMS)
  - Security controls
  - Risk assessment

- **COBIT Framework**
  - Governance and management
  - Business alignment

#### 2.2 Risk Management
- **Risk Assessment Process**
  - Asset identification
  - Threat identification
  - Vulnerability assessment
  - Risk calculation
  - Risk treatment options

- **Security Controls**
  - Administrative controls
  - Technical controls
  - Physical controls
  - Preventive vs Detective vs Corrective

### Week 3: Practical Implementation

#### 3.1 Security Tools Introduction
- **Vulnerability Scanners**
  - Nessus basics
  - OpenVAS introduction
  - Vulnerability databases (CVE, NVD)

- **Network Security Tools**
  - Nmap for network discovery
  - Wireshark for packet analysis
  - Basic firewall concepts

#### 3.2 Security Best Practices
- **Personal Security**
  - Password management
  - Multi-factor authentication
  - Safe browsing habits
  - Social media security

- **Organizational Security**
  - Security policies
  - Employee training
  - Incident reporting
  - Business continuity planning

## 🛠️ Hands-on Labs

### Lab 1: Security Assessment Basics
**Objective**: Perform basic security assessment menggunakan tools dasar

**Tasks**:
1. Install dan configure Nmap
2. Scan network untuk discover hosts
3. Identify open ports dan services
4. Document findings dalam report format

**Tools**: Nmap, Nessus Community Edition

### Lab 2: Password Security Analysis
**Objective**: Analyze password strength dan implement best practices

**Tasks**:
1. Use password cracking tools (John the Ripper)
2. Analyze common password patterns
3. Implement password policy
4. Setup password manager

**Tools**: John the Ripper, Hashcat, KeePass

### Lab 3: Network Traffic Analysis
**Objective**: Capture dan analyze network traffic untuk security insights

**Tasks**:
1. Setup Wireshark packet capture
2. Analyze HTTP vs HTTPS traffic
3. Identify suspicious network activity
4. Create traffic analysis report

**Tools**: Wireshark, tcpdump

## 🎯 Project Challenge: Personal Security Audit

### Project Overview
Lakukan comprehensive security audit terhadap environment personal Anda dan buat improvement plan.

### Deliverables:
1. **Asset Inventory** (1 week)
   - List semua devices dan accounts
   - Classify berdasarkan sensitivity
   - Document current security measures

2. **Risk Assessment** (1 week)
   - Identify potential threats
   - Assess vulnerabilities
   - Calculate risk levels
   - Prioritize risks

3. **Security Implementation Plan** (1 week)
   - Recommend security controls
   - Implementation timeline
   - Cost-benefit analysis
   - Monitoring plan

### Assessment Criteria:
- **Completeness** (25%): Thoroughness of audit
- **Technical Accuracy** (25%): Correct use of concepts
- **Risk Analysis** (25%): Quality of risk assessment
- **Practical Solutions** (25%): Feasibility of recommendations

## 📋 Assessment Methods

### Theory Assessment (40%)
- **Quiz 1**: CIA Triad dan basic concepts (Week 1)
- **Quiz 2**: Threat landscape dan actors (Week 2)
- **Final Exam**: Comprehensive theory test (Week 3)

### Practical Assessment (40%)
- **Lab Reports**: Documentation of lab activities
- **Tool Proficiency**: Hands-on tool usage
- **Technical Skills**: Problem-solving abilities

### Project Assessment (20%)
- **Personal Security Audit**: Comprehensive project deliverable

## 📚 Required Reading

### Books:
1. "Cybersecurity Essentials" - Charles J. Brooks
2. "The Art of Deception" - Kevin Mitnick
3. "Security+ Guide to Network Security Fundamentals" - Mark Ciampa

### Online Resources:
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [SANS Reading Room](https://www.sans.org/reading-room/)
- [Krebs on Security Blog](https://krebsonsecurity.com/)
- [OWASP Foundation](https://owasp.org/)

### Videos:
- Professor Messer Security+ Course
- Cybrary Cybersecurity Fundamentals
- SANS Cyber Aces Tutorials

## 🔧 Tools & Software

### Required Tools:
- **Nmap** - Network discovery dan security auditing
- **Wireshark** - Network protocol analyzer
- **Nessus** - Vulnerability scanner
- **John the Ripper** - Password cracking tool
- **KeePass** - Password manager

### Optional Tools:
- **Metasploit Community** - Penetration testing framework
- **Burp Suite Community** - Web application security testing
- **YARA** - Malware identification dan classification

## 🎓 Certification Preparation

Modul ini mempersiapkan Anda untuk:
- **CompTIA Security+** (SY0-601)
- **ISC2 Systems Security Certified Practitioner (SSCP)**
- **EC-Council Computer Hacking Forensic Investigator Associate (CHFIA)**

## ➡️ Next Steps

Setelah menyelesaikan Module 1, Anda siap untuk:
- [Module 2: Network Security](../module-02-network-security/)
- Specialized tracks berdasarkan interest area
- Industry certification preparation

---

**🚀 Ready to start? Begin with [Week 1: Foundation Concepts](./week-01/)**
