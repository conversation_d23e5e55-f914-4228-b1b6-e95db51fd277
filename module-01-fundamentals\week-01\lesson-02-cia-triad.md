# 🔐 Lesson 1.2: CIA Triad - Core Security Principles

## 🎯 Learning Objectives
- Memahami konsep CIA Triad sebagai foundation cybersecurity
- Menjelaskan Confidentiality, Integrity, dan Availability secara detail
- Mengidentifikasi threats terhadap masing-masing komponen CIA
- Menerapkan controls untuk melindungi CIA Triad
- Menganalisis real-world scenarios menggunakan CIA framework

## 📖 Introduction to CIA Triad

**CIA Triad** adalah model fundamental dalam information security yang terdiri dari tiga prinsip utama:
- **C**onfidentiality (Kerahasiaan)
- **I**ntegrity (Integritas)  
- **A**vailability (Ketersediaan)

Model ini menjadi foundation untuk semua security policies, procedures, dan controls dalam organisasi.

```
        CIA TRIAD
           /\
          /  \
         /    \
        /______\
   Confidentiality
      /        \
     /          \
    /____________\
Integrity    Availability
```

## 🔒 Confidentiality (Kerahasiaan)

### Definition
**Confidentiality** memastikan bahwa informasi hanya dapat diakses oleh pihak yang berwenang dan melindungi data dari unauthorized disclosure.

### Key Concepts

#### 1. Data Classification
Sistem klasifikasi untuk menentukan level protection yang dibutuhkan:

**Government Classification**:
- **Top Secret**: Damage to national security
- **Secret**: Serious damage to national security  
- **Confidential**: Damage to national security
- **Unclassified**: No damage to national security

**Commercial Classification**:
- **Confidential**: Competitive advantage, trade secrets
- **Internal Use**: Internal business information
- **Public**: Information available to public

#### 2. Access Controls
Mechanisms untuk enforce confidentiality:

**Discretionary Access Control (DAC)**:
- Owner menentukan access permissions
- Flexible tapi less secure
- Example: File permissions di Windows/Linux

**Mandatory Access Control (MAC)**:
- System menentukan access berdasarkan labels
- More secure tapi less flexible
- Example: SELinux, military systems

**Role-Based Access Control (RBAC)**:
- Access berdasarkan job roles
- Balance antara security dan usability
- Example: Enterprise applications

#### 3. Encryption
Proses mengubah plaintext menjadi ciphertext:

**Symmetric Encryption**:
- Same key untuk encrypt dan decrypt
- Fast dan efficient
- Key distribution challenge
- Examples: AES, DES, 3DES

**Asymmetric Encryption**:
- Different keys (public/private)
- Slower tapi solves key distribution
- Examples: RSA, ECC

### Threats to Confidentiality
1. **Unauthorized Access**
   - Weak passwords
   - Privilege escalation
   - Social engineering

2. **Data Leakage**
   - Insider threats
   - Misconfigured systems
   - Unencrypted communications

3. **Eavesdropping**
   - Network sniffing
   - Man-in-the-middle attacks
   - Shoulder surfing

### Controls for Confidentiality
- **Technical**: Encryption, access controls, firewalls
- **Administrative**: Security policies, training, background checks
- **Physical**: Locked doors, security cameras, clean desk policy

## ✅ Integrity (Integritas)

### Definition
**Integrity** memastikan bahwa data dan systems accurate, complete, dan tidak dimodifikasi secara unauthorized.

### Types of Integrity

#### 1. Data Integrity
Memastikan data tidak berubah selama storage atau transmission:

**Hash Functions**:
- MD5 (deprecated - vulnerable)
- SHA-1 (deprecated - vulnerable)
- SHA-256 (current standard)
- SHA-3 (latest standard)

**Digital Signatures**:
- Combines hashing dengan asymmetric encryption
- Provides authentication dan non-repudiation
- Example: Code signing certificates

#### 2. System Integrity
Memastikan systems dan applications tidak dimodifikasi:

**File Integrity Monitoring (FIM)**:
- Monitor changes to critical files
- Alert on unauthorized modifications
- Examples: Tripwire, AIDE

**System Hardening**:
- Remove unnecessary services
- Apply security patches
- Configure secure settings

#### 3. Network Integrity
Memastikan data tidak dimodifikasi during transmission:

**Message Authentication Codes (MAC)**:
- HMAC (Hash-based MAC)
- Verifies message integrity dan authenticity

**Checksums**:
- Simple integrity verification
- CRC32, MD5 checksums
- Limited security value

### Threats to Integrity
1. **Unauthorized Modification**
   - Malware infections
   - Insider threats
   - Privilege escalation

2. **Data Corruption**
   - Hardware failures
   - Software bugs
   - Power outages

3. **Man-in-the-Middle Attacks**
   - Network interception
   - Data modification in transit
   - Session hijacking

### Controls for Integrity
- **Technical**: Digital signatures, checksums, version control
- **Administrative**: Change management, separation of duties
- **Physical**: Environmental controls, hardware security

## 🔄 Availability (Ketersediaan)

### Definition
**Availability** memastikan bahwa systems, applications, dan data accessible ketika dibutuhkan oleh authorized users.

### Key Concepts

#### 1. Uptime Requirements
Measurement of system availability:

**Availability Percentages**:
- 99% = 3.65 days downtime/year
- 99.9% = 8.77 hours downtime/year
- 99.99% = 52.6 minutes downtime/year
- 99.999% = 5.26 minutes downtime/year

#### 2. Redundancy
Multiple systems untuk prevent single points of failure:

**Hardware Redundancy**:
- RAID arrays untuk storage
- Multiple power supplies
- Backup network connections

**Geographic Redundancy**:
- Multiple data centers
- Disaster recovery sites
- Cloud availability zones

#### 3. Fault Tolerance
System's ability untuk continue operating despite failures:

**Clustering**:
- Multiple servers working together
- Automatic failover
- Load distribution

**Load Balancing**:
- Distribute traffic across multiple servers
- Improve performance dan availability
- Health monitoring

### Threats to Availability
1. **Denial of Service (DoS)**
   - Network flooding
   - Resource exhaustion
   - Application layer attacks

2. **Distributed Denial of Service (DDoS)**
   - Multiple attack sources
   - Difficult to block
   - Amplification attacks

3. **System Failures**
   - Hardware malfunctions
   - Software crashes
   - Power outages

4. **Natural Disasters**
   - Earthquakes, floods, fires
   - Extended power outages
   - Infrastructure damage

### Controls for Availability
- **Technical**: Redundancy, backups, monitoring
- **Administrative**: Incident response, maintenance procedures
- **Physical**: UPS systems, environmental controls

## 🔄 CIA Triad Relationships

### Balancing the Triad
Often ada trade-offs antara ketiga komponen:

**Security vs Usability**:
- Strong authentication (confidentiality) vs user convenience
- Encryption (confidentiality) vs system performance (availability)
- Change controls (integrity) vs business agility

**Real-World Examples**:
1. **Banking System**:
   - High confidentiality: Strong encryption, access controls
   - High integrity: Transaction verification, audit trails
   - High availability: 24/7 operations, redundancy

2. **Public Website**:
   - Low confidentiality: Public information
   - Medium integrity: Content accuracy important
   - High availability: Always accessible to users

3. **Medical Records**:
   - High confidentiality: Patient privacy (HIPAA)
   - High integrity: Accurate medical information critical
   - Medium availability: Accessible during business hours

## 🛠️ Practical Exercises

### Exercise 1: CIA Analysis
**Objective**: Analyze systems menggunakan CIA framework

**Scenario**: Online banking application

**Tasks**:
1. Identify confidentiality requirements
2. Determine integrity needs
3. Define availability expectations
4. Suggest appropriate controls for each

### Exercise 2: Threat Mapping
**Objective**: Map threats to CIA components

**Instructions**:
1. List 10 common cybersecurity threats
2. Categorize each threat by primary CIA impact
3. Suggest mitigation strategies
4. Rank threats by potential business impact

### Exercise 3: Control Implementation
**Objective**: Design controls untuk protect CIA

**Scenario**: Small business dengan 50 employees

**Requirements**:
- Design access control system
- Implement data backup strategy
- Create incident response plan
- Justify control selections

## 📊 Real-World Case Studies

### Case Study 1: Confidentiality Breach - Equifax (2017)
**What Happened**: 147 million personal records exposed
**CIA Impact**: Massive confidentiality breach
**Root Cause**: Unpatched vulnerability
**Lessons**:
- Importance of patch management
- Need for data encryption
- Value of access controls

### Case Study 2: Integrity Attack - Ukraine Power Grid (2015)
**What Happened**: Attackers modified control systems
**CIA Impact**: System integrity compromised
**Root Cause**: Spear phishing dan lateral movement
**Lessons**:
- Critical infrastructure protection
- Network segmentation importance
- System integrity monitoring

### Case Study 3: Availability Attack - Dyn DNS DDoS (2016)
**What Happened**: Major websites unavailable for hours
**CIA Impact**: Widespread availability loss
**Root Cause**: IoT botnet DDoS attack
**Lessons**:
- DNS infrastructure vulnerability
- IoT security importance
- DDoS mitigation strategies

## ✅ Knowledge Check

### Quiz Questions:
1. What are the three components of CIA Triad?
2. Give an example of a threat to each CIA component.
3. How does encryption protect confidentiality?
4. What is the difference between data integrity dan system integrity?
5. Calculate annual downtime for 99.95% availability.

### Scenario Analysis:
**Scenario**: E-commerce website during Black Friday

**Questions**:
1. Which CIA component is most critical? Why?
2. What threats are most likely during high traffic?
3. What controls would you implement?
4. How would you measure success?

## 📚 Additional Resources

### Standards & Frameworks:
- ISO 27001/27002 - Information Security Management
- NIST SP 800-53 - Security Controls
- COBIT - IT Governance Framework

### Tools untuk CIA Implementation:
- **Confidentiality**: VeraCrypt, GnuPG, Azure Information Protection
- **Integrity**: Tripwire, AIDE, Git
- **Availability**: Nagios, Zabbix, AWS CloudWatch

---

**Next**: [Lesson 1.3: Threat Landscape Overview](./lesson-03-threat-landscape.md)
