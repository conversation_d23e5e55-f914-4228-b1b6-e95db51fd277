# 🌐 Module 3: Web Application Security

**Level**: Intermediate  
**Duration**: 4-5 weeks  
**Prerequisites**: Module 1-2 completed, basic web development knowledge

## 📚 Learning Objectives

Setelah menyelesaikan modul ini, Anda akan mampu:
- ✅ Memahami OWASP Top 10 vulnerabilities secara mendalam
- ✅ Melakukan web application penetration testing
- ✅ Menggunakan tools untuk web security assessment
- ✅ Mengimplementasikan secure coding practices
- ✅ Melakukan source code review untuk security issues
- ✅ Memahami web application firewalls (WAF)
- ✅ Mengamankan APIs dan microservices
- ✅ Melakukan bug bounty hunting

## 📖 Course Content

### Week 1: Web Application Fundamentals & OWASP Top 10 (Part 1)

#### 1.1 Web Application Architecture Security
- **Client-Server Architecture**
  - Browser security model
  - Same-origin policy
  - Cross-origin resource sharing (CORS)
  - Content security policy (CSP)

- **Web Technologies Security**
  - HTML5 security features
  - JavaScript security considerations
  - CSS security implications
  - HTTP/HTTPS protocol security

#### 1.2 OWASP Top 10 (2021) - Part 1
- **A01: Broken Access Control**
  - Vertical privilege escalation
  - Horizontal privilege escalation
  - IDOR (Insecure Direct Object References)
  - Missing function level access control

- **A02: Cryptographic Failures**
  - Weak encryption algorithms
  - Poor key management
  - Insufficient transport layer protection
  - Weak random number generation

- **A03: Injection**
  - SQL injection (SQLi)
  - NoSQL injection
  - LDAP injection
  - Command injection
  - XPath injection

### Week 2: OWASP Top 10 (Part 2) & Advanced Attacks

#### 2.1 OWASP Top 10 (2021) - Part 2
- **A04: Insecure Design**
  - Threat modeling
  - Secure design principles
  - Security requirements
  - Design pattern vulnerabilities

- **A05: Security Misconfiguration**
  - Default configurations
  - Unnecessary features enabled
  - Missing security headers
  - Error handling information disclosure

- **A06: Vulnerable and Outdated Components**
  - Dependency management
  - Component inventory
  - Vulnerability scanning
  - Update management

#### 2.2 Advanced Web Attacks
- **A07: Identification and Authentication Failures**
  - Weak password policies
  - Session management flaws
  - Credential stuffing
  - Brute force attacks

- **A08: Software and Data Integrity Failures**
  - Insecure deserialization
  - Supply chain attacks
  - Code integrity verification
  - CI/CD pipeline security

### Week 3: Client-Side Security & Modern Web Threats

#### 3.1 Client-Side Vulnerabilities
- **A09: Security Logging and Monitoring Failures**
  - Insufficient logging
  - Log injection
  - Missing alerting
  - Incident response gaps

- **A10: Server-Side Request Forgery (SSRF)**
  - Internal network access
  - Cloud metadata exploitation
  - Port scanning via SSRF
  - Blind SSRF techniques

#### 3.2 Advanced Client-Side Attacks
- **Cross-Site Scripting (XSS)**
  - Reflected XSS
  - Stored XSS
  - DOM-based XSS
  - XSS filter bypass techniques

- **Cross-Site Request Forgery (CSRF)**
  - CSRF token implementation
  - SameSite cookie attribute
  - Double submit cookies
  - Custom headers protection

### Week 4: API Security & Modern Architectures

#### 4.1 API Security
- **REST API Security**
  - Authentication mechanisms (JWT, OAuth)
  - Rate limiting
  - Input validation
  - Error handling

- **GraphQL Security**
  - Query complexity analysis
  - Depth limiting
  - Introspection attacks
  - Authorization in GraphQL

#### 4.2 Modern Web Architecture Security
- **Single Page Applications (SPA)**
  - Token-based authentication
  - Secure storage in browsers
  - CORS configuration
  - CSP implementation

- **Microservices Security**
  - Service-to-service authentication
  - API gateway security
  - Container security
  - Service mesh security

### Week 5: Secure Development & Defense

#### 5.1 Secure Coding Practices
- **Input Validation**
  - Whitelist vs blacklist
  - Parameterized queries
  - Output encoding
  - Canonicalization

- **Authentication & Session Management**
  - Strong authentication mechanisms
  - Secure session handling
  - Password storage best practices
  - Multi-factor authentication

#### 5.2 Web Application Firewalls & Defense
- **WAF Implementation**
  - Rule configuration
  - Custom rule creation
  - False positive management
  - Performance considerations

- **Runtime Application Self-Protection (RASP)**
  - Real-time threat detection
  - Application-level monitoring
  - Automated response mechanisms

## 🛠️ Hands-on Labs

### Lab 1: OWASP Top 10 Exploitation
**Objective**: Exploit OWASP Top 10 vulnerabilities dalam controlled environment

**Target Applications**:
- DVWA (Damn Vulnerable Web Application)
- WebGoat
- Mutillidae II
- bWAPP

**Tasks**:
1. SQL injection exploitation
2. XSS payload development
3. CSRF attack implementation
4. Access control bypass
5. File upload vulnerabilities

**Tools**: Burp Suite, OWASP ZAP, SQLmap

### Lab 2: Web Application Penetration Testing
**Objective**: Perform comprehensive web application security assessment

**Methodology**:
1. Information gathering
2. Vulnerability scanning
3. Manual testing
4. Exploitation
5. Post-exploitation
6. Reporting

**Tasks**:
1. Automated scanning dengan OWASP ZAP
2. Manual testing dengan Burp Suite
3. Source code analysis
4. Business logic testing
5. Authentication bypass attempts

**Tools**: Burp Suite Professional, OWASP ZAP, Nikto, Dirb

### Lab 3: Secure Code Review
**Objective**: Identify security vulnerabilities dalam source code

**Languages Covered**:
- PHP
- Java
- Python
- JavaScript/Node.js

**Tasks**:
1. Static code analysis
2. Manual code review
3. Vulnerability identification
4. Remediation recommendations
5. Secure coding guidelines

**Tools**: SonarQube, Checkmarx, Veracode, ESLint

### Lab 4: API Security Testing
**Objective**: Test REST dan GraphQL APIs untuk security vulnerabilities

**Tasks**:
1. API discovery dan enumeration
2. Authentication testing
3. Authorization bypass
4. Input validation testing
5. Rate limiting assessment
6. GraphQL specific attacks

**Tools**: Postman, Insomnia, GraphQL Playground, Burp Suite

## 🎯 Project Challenge: Bug Bounty Simulation

### Project Overview
Participate dalam simulated bug bounty program dengan multiple target applications.

### Target Applications:
1. **E-commerce Platform** - Shopping cart, payment processing
2. **Social Media App** - User profiles, messaging, file uploads
3. **Banking Application** - Account management, transactions
4. **API Gateway** - Microservices communication

### Hunting Methodology:

#### Phase 1: Reconnaissance (Week 1)
- **Passive Information Gathering**
  - Domain enumeration
  - Subdomain discovery
  - Technology stack identification
  - Social media intelligence

- **Active Reconnaissance**
  - Port scanning
  - Service enumeration
  - Directory brute forcing
  - Parameter discovery

#### Phase 2: Vulnerability Discovery (Week 2-3)
- **Automated Scanning**
  - Vulnerability scanners
  - Custom scripts
  - Fuzzing techniques

- **Manual Testing**
  - Business logic flaws
  - Authentication bypass
  - Authorization issues
  - Input validation problems

#### Phase 3: Exploitation & Reporting (Week 4)
- **Proof of Concept Development**
  - Exploit creation
  - Impact demonstration
  - Risk assessment

- **Professional Reporting**
  - Vulnerability description
  - Reproduction steps
  - Impact analysis
  - Remediation recommendations

### Scoring System:
- **Critical**: 1000 points
- **High**: 500 points
- **Medium**: 250 points
- **Low**: 100 points
- **Informational**: 50 points

### Assessment Criteria:
- **Vulnerability Discovery** (40%): Number dan severity of findings
- **Exploitation Quality** (25%): PoC effectiveness dan creativity
- **Report Quality** (20%): Professional documentation
- **Methodology** (15%): Systematic approach dan thoroughness

## 📋 Assessment Methods

### Theory Assessment (30%)
- **Weekly Quizzes**: OWASP Top 10, web technologies
- **Midterm Exam**: Web application security fundamentals
- **Final Exam**: Comprehensive web security knowledge

### Practical Assessment (50%)
- **Lab Reports**: Detailed vulnerability analysis
- **Tool Proficiency**: Effective use of security tools
- **Exploitation Skills**: Successful vulnerability exploitation

### Project Assessment (20%)
- **Bug Bounty Simulation**: Comprehensive security assessment

## 🔧 Required Tools & Software

### Web Application Scanners:
- **Burp Suite Professional** - Comprehensive web security testing
- **OWASP ZAP** - Free web application security scanner
- **Nikto** - Web server scanner
- **Dirb/Dirbuster** - Directory brute forcing

### Specialized Tools:
- **SQLmap** - Automated SQL injection testing
- **XSSer** - Cross-site scripting detection
- **Commix** - Command injection testing
- **NoSQLMap** - NoSQL injection testing

### Code Analysis:
- **SonarQube** - Static code analysis
- **Bandit** - Python security linter
- **ESLint** - JavaScript security rules
- **Brakeman** - Ruby on Rails security scanner

### API Testing:
- **Postman** - API development dan testing
- **Insomnia** - REST client
- **GraphQL Playground** - GraphQL testing
- **SOAP UI** - SOAP service testing

### Browser Extensions:
- **Wappalyzer** - Technology detection
- **Cookie Editor** - Cookie manipulation
- **User-Agent Switcher** - Browser fingerprint modification

## 📚 Required Reading

### Books:
1. "The Web Application Hacker's Handbook" - Dafydd Stuttard
2. "Real-World Bug Hunting" - Peter Yaworski
3. "Web Security for Developers" - Malcolm McDonald
4. "The Tangled Web" - Michal Zalewski

### OWASP Resources:
- [OWASP Top 10 2021](https://owasp.org/Top10/)
- [OWASP Testing Guide](https://owasp.org/www-project-web-security-testing-guide/)
- [OWASP Code Review Guide](https://owasp.org/www-project-code-review-guide/)
- [OWASP API Security Top 10](https://owasp.org/www-project-api-security/)

### Online Platforms:
- [PortSwigger Web Security Academy](https://portswigger.net/web-security)
- [HackerOne Hacktivity](https://hackerone.com/hacktivity)
- [Bugcrowd University](https://www.bugcrowd.com/hackers/bugcrowd-university/)

## 🎓 Certification Preparation

Modul ini mempersiapkan Anda untuk:
- **CEH (Certified Ethical Hacker)** - Web application testing
- **OSCP** - Web application exploitation
- **GWEB (GIAC Web Application Penetration Tester)**
- **eWPT (eLearnSecurity Web Application Penetration Tester)**

## ➡️ Next Steps

Setelah menyelesaikan Module 3, Anda siap untuk:
- [Module 4: Penetration Testing](../module-04-penetration-testing/)
- Bug bounty hunting programs
- Web application security specialization

---

**🚀 Ready to hack the web? Begin with [Week 1: Web Fundamentals & OWASP Top 10](./week-01/)**
