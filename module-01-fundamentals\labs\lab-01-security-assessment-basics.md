# 🔬 Lab 1: Security Assessment Basics

**Duration**: 3-4 hours  
**Difficulty**: Beginner  
**Prerequisites**: Basic command line knowledge

## 🎯 Lab Objectives

Setelah menyelesaikan lab ini, Anda akan mampu:
- ✅ Menggunakan Nmap untuk network discovery dan port scanning
- ✅ Menginterpretasi hasil scan untuk identify potential vulnerabilities
- ✅ Menggunakan Nessus untuk vulnerability assessment
- ✅ Membuat professional security assessment report
- ✅ Memahami ethical dan legal considerations dalam security testing

## 🛠️ Required Tools

### Primary Tools:
- **Nmap** - Network discovery dan security auditing
- **Nessus Community Edition** - Vulnerability scanner
- **Kali Linux VM** - Testing platform

### Target Environment:
- **Metasploitable 2** - Intentionally vulnerable Linux
- **DVWA** - Damn Vulnerable Web Application
- **Local network** - Your lab environment

## ⚠️ Legal & Ethical Disclaimer

**IMPORTANT**: Only perform these tests on:
- Your own systems
- Lab environments you own
- Systems you have explicit written permission to test

**NEVER** scan or test systems you don't own without permission. Unauthorized scanning dapat melanggar hukum dan result in serious legal consequences.

## 📋 Lab Setup

### Step 1: Prepare Target Environment
```bash
# Download Metasploitable 2
wget https://sourceforge.net/projects/metasploitable/files/Metasploitable2/metasploitable-linux-2.0.0.zip

# Extract dan import to VMware/VirtualBox
# Configure network: Host-Only (*************/24)
# Default credentials: msfadmin/msfadmin
```

### Step 2: Verify Network Connectivity
```bash
# From Kali Linux, test connectivity
ping **************  # Metasploitable IP
nmap -sn *************/24  # Network discovery
```

### Step 3: Install Required Tools
```bash
# Update Kali Linux
sudo apt update && sudo apt upgrade -y

# Install additional tools
sudo apt install -y nmap nessus
```

## 🔍 Exercise 1: Network Discovery with Nmap

### Objective
Discover hosts dan services dalam lab network menggunakan Nmap.

### Tasks

#### Task 1.1: Host Discovery
```bash
# Basic ping sweep
nmap -sn *************/24

# ARP scan (more reliable on local network)
nmap -PR *************/24

# TCP SYN ping
nmap -PS *************/24
```

**Questions**:
1. How many hosts did you discover?
2. Which discovery method was most effective? Why?
3. What information does each scan provide?

#### Task 1.2: Port Scanning
```bash
# Basic TCP scan
nmap **************

# Comprehensive TCP scan
nmap -sS -O -sV **************

# UDP scan (slower)
nmap -sU --top-ports 100 **************

# Aggressive scan (combines multiple options)
nmap -A **************
```

**Analysis Questions**:
1. Which ports are open on the target?
2. What services are running on each port?
3. What operating system is the target running?
4. Which scan took the longest? Why?

#### Task 1.3: Script Scanning
```bash
# Default scripts
nmap --script default **************

# Vulnerability scripts
nmap --script vuln **************

# Specific service scripts
nmap --script ssh-* **************
nmap --script http-* **************
```

**Documentation**:
Create a table documenting:
- Port number
- Service name
- Version information
- Potential vulnerabilities found

### Expected Results
You should discover services like:
- SSH (port 22)
- HTTP (port 80)
- MySQL (port 3306)
- VNC (port 5900)
- And many others...

## 🔒 Exercise 2: Vulnerability Assessment with Nessus

### Objective
Perform comprehensive vulnerability assessment menggunakan Nessus scanner.

### Setup Nessus

#### Step 2.1: Install dan Configure Nessus
```bash
# Download Nessus from Tenable website
# Install Nessus Community Edition
sudo dpkg -i Nessus-*.deb

# Start Nessus service
sudo systemctl start nessusd

# Access web interface
https://localhost:8834
```

#### Step 2.2: Initial Configuration
1. Create admin account
2. Register for Community license
3. Download plugins (this takes time)
4. Create new scan policy

### Vulnerability Scanning

#### Task 2.1: Basic Network Scan
1. **Create New Scan**:
   - Template: Basic Network Scan
   - Name: "Lab Network Assessment"
   - Targets: **************

2. **Configure Scan Settings**:
   - Discovery: Port scan (all ports)
   - Assessment: Scan for known web vulnerabilities
   - Credentials: Add SSH credentials (msfadmin/msfadmin)

3. **Launch Scan** dan wait for completion

#### Task 2.2: Analyze Results
Review scan results dan document:

**Critical Vulnerabilities**:
- CVE numbers
- CVSS scores
- Affected services
- Potential impact

**High Vulnerabilities**:
- Service misconfigurations
- Weak authentication
- Unpatched software

**Medium/Low Vulnerabilities**:
- Information disclosure
- Minor misconfigurations

#### Task 2.3: Vulnerability Verification
Manually verify beberapa findings:

```bash
# Example: Verify SSH version
ssh -V **************

# Example: Check web server headers
curl -I http://**************

# Example: Test for default credentials
mysql -h ************** -u root -p
```

## 📊 Exercise 3: Report Generation

### Objective
Create professional security assessment report berdasarkan findings.

### Report Structure

#### Executive Summary
- **Scope**: Systems tested
- **Methodology**: Tools dan techniques used
- **Key Findings**: High-level summary
- **Risk Rating**: Overall risk assessment
- **Recommendations**: Priority actions

#### Technical Findings

**Template untuk each vulnerability**:
```
Vulnerability: [Name]
Severity: [Critical/High/Medium/Low]
CVE: [CVE-YYYY-XXXX]
CVSS Score: [X.X]
Affected Systems: [IP addresses/hostnames]
Description: [Technical description]
Impact: [Potential business impact]
Recommendation: [Specific remediation steps]
References: [Links to additional information]
```

#### Risk Assessment Matrix
Create matrix showing:
- Likelihood vs Impact
- Risk ratings for each finding
- Prioritization recommendations

### Sample Report Sections

#### Executive Summary Example:
```
EXECUTIVE SUMMARY

During the security assessment of the lab environment conducted on [DATE], 
several critical vulnerabilities were identified that pose significant risk 
to the confidentiality, integrity, and availability of systems and data.

Key Findings:
- 15 Critical vulnerabilities requiring immediate attention
- 23 High-risk vulnerabilities requiring remediation within 30 days
- 45 Medium/Low risk vulnerabilities for future consideration

The most critical findings include:
1. Unpatched SSH service vulnerable to remote code execution
2. Default database credentials allowing unauthorized access
3. Web application vulnerabilities enabling data theft

Immediate action is required to address critical vulnerabilities before 
systems are exposed to production networks.
```

## 🎯 Challenge Exercise: Advanced Scanning

### Objective
Perform advanced scanning techniques dan evasion methods.

### Advanced Nmap Techniques

#### Stealth Scanning
```bash
# SYN stealth scan
nmap -sS **************

# FIN scan (firewall evasion)
nmap -sF **************

# Fragmented packets
nmap -f **************

# Decoy scanning
nmap -D RND:10 **************
```

#### Timing dan Performance
```bash
# Slow scan (stealth)
nmap -T1 **************

# Fast scan
nmap -T4 **************

# Aggressive timing
nmap -T5 **************
```

#### Custom Scripts
```bash
# List available scripts
nmap --script-help vuln

# Run specific vulnerability checks
nmap --script smb-vuln-* **************
nmap --script http-sql-injection **************
```

## 📋 Lab Deliverables

### Required Submissions:

1. **Nmap Scan Results** (30 points)
   - Host discovery output
   - Port scan results
   - Service version detection
   - Script scan findings

2. **Nessus Assessment Report** (40 points)
   - Complete vulnerability scan results
   - Risk categorization
   - Verification of key findings
   - Screenshots of critical vulnerabilities

3. **Professional Security Report** (30 points)
   - Executive summary
   - Technical findings
   - Risk assessment
   - Remediation recommendations
   - Proper formatting dan presentation

### Grading Criteria:

**Technical Accuracy (40%)**:
- Correct use of tools
- Accurate interpretation of results
- Proper vulnerability identification

**Documentation Quality (30%)**:
- Clear dan organized presentation
- Professional report format
- Appropriate technical detail

**Analysis Depth (20%)**:
- Understanding of security implications
- Risk assessment accuracy
- Quality of recommendations

**Completeness (10%)**:
- All required elements included
- Thorough coverage of assigned tasks

## 🔧 Troubleshooting Guide

### Common Issues:

**Nmap Not Finding Hosts**:
```bash
# Check network connectivity
ping **************

# Verify network interface
ip addr show

# Try different discovery methods
nmap -Pn **************  # Skip ping
```

**Nessus Plugin Issues**:
- Ensure plugins are fully downloaded
- Check internet connectivity
- Restart Nessus service if needed

**Permission Errors**:
```bash
# Run with sudo if needed
sudo nmap -sS **************

# Check user permissions
whoami
groups
```

## 📚 Additional Resources

### Nmap Resources:
- [Official Nmap Documentation](https://nmap.org/docs.html)
- [Nmap Network Scanning Book](https://nmap.org/book/)
- [Nmap Scripting Engine](https://nmap.org/book/nse.html)

### Nessus Resources:
- [Tenable Documentation](https://docs.tenable.com/nessus/)
- [Vulnerability Database](https://www.tenable.com/plugins)
- [CVSS Calculator](https://www.first.org/cvss/calculator/3.1)

### Report Writing:
- [NIST SP 800-115](https://csrc.nist.gov/publications/detail/sp/800-115/final) - Technical Guide to Information Security Testing
- [OWASP Testing Guide](https://owasp.org/www-project-web-security-testing-guide/)

---

**Next Lab**: [Lab 2: Password Security Analysis](./lab-02-password-security.md)
