# 📋 Prerequisites Assessment

**Duration**: 1-2 hours  
**Purpose**: Evaluate readiness untuk cybersecurity learning path

## 🎯 Assessment Overview

Assessment ini dirancang untuk:
- ✅ Mengukur current knowledge level
- ✅ Mengidentifikasi knowledge gaps
- ✅ Menentukan starting point yang tepat
- ✅ Memberikan personalized learning recommendations

## 📊 Assessment Sections

### Section A: Basic Computer Knowledge (25%)

#### A1. Operating Systems
**Questions (Multiple Choice)**:

1. Which command is used to list files in Linux?
   a) dir
   b) ls
   c) list
   d) show

2. What is the purpose of Windows Registry?
   a) Store user files
   b) Store system configuration
   c) Store temporary files
   d) Store application data

3. Which file system is commonly used in Linux?
   a) NTFS
   b) FAT32
   c) ext4
   d) HFS+

#### A2. Command Line Basics
**Practical Tasks**:

1. **Windows Command Prompt**:
   ```cmd
   # Navigate to C:\Windows\System32
   # List all files containing "net"
   # Display IP configuration
   ```

2. **Linux Terminal**:
   ```bash
   # Create directory structure: /tmp/test/cybersec
   # Create file with current date
   # Change file permissions to 755
   ```

### Section B: Networking Fundamentals (30%)

#### B1. Network Concepts
**Questions**:

1. What is the default subnet mask for Class C network?
   a) *********
   b) ***********
   c) *************
   d) ***************

2. Which port does HTTPS use by default?
   a) 80
   b) 443
   c) 8080
   d) 8443

3. What does DNS stand for?
   a) Domain Name System
   b) Dynamic Network Service
   c) Distributed Name Server
   d) Direct Network System

#### B2. TCP/IP Stack
**Scenario-based Questions**:

1. **Network Troubleshooting**:
   ```
   Scenario: User cannot access website www.example.com
   
   Questions:
   - What commands would you use to troubleshoot?
   - Explain the DNS resolution process
   - How would you test connectivity at each layer?
   ```

2. **Packet Analysis**:
   ```
   Given packet capture showing:
   Source: *************:3456
   Destination: *********:80
   Protocol: TCP
   Flags: SYN
   
   Questions:
   - What is happening in this packet?
   - What would you expect in the response?
   - Is this normal traffic?
   ```

### Section C: Security Awareness (25%)

#### C1. Basic Security Concepts
**Questions**:

1. What does CIA stand for in cybersecurity?
   a) Central Intelligence Agency
   b) Confidentiality, Integrity, Availability
   c) Computer Information Assurance
   d) Cyber Intelligence Analysis

2. Which is the strongest password?
   a) Password123
   b) P@ssw0rd!
   c) MyDog'sName2023!
   d) Tr0ub4dor&3

3. What is phishing?
   a) Network scanning technique
   b) Social engineering attack via email
   c) Password cracking method
   d) Firewall bypass technique

#### C2. Threat Recognition
**Scenario Analysis**:

1. **Email Security**:
   ```
   Email Subject: "Urgent: Verify Your Account"
   From: <EMAIL>
   Content: "Click here to verify your account within 24 hours 
   or it will be suspended: http://verify-account.suspicious-site.com"
   
   Questions:
   - What red flags do you notice?
   - How would you verify if this is legitimate?
   - What should you do with this email?
   ```

2. **Social Engineering**:
   ```
   Scenario: Someone calls claiming to be from IT support,
   asking for your password to "fix a security issue"
   
   Questions:
   - How would you respond?
   - What verification steps would you take?
   - What policies should be in place?
   ```

### Section D: Technical Aptitude (20%)

#### D1. Problem Solving
**Logic Puzzles**:

1. **Pattern Recognition**:
   ```
   Sequence: ***********, ***********, ***********, ***********
   
   Questions:
   - What is the pattern?
   - What are the next 3 IP addresses?
   - What networking concept does this represent?
   ```

2. **System Analysis**:
   ```
   Log Entry: "Failed login attempt for user 'admin' from ***********"
   
   Questions:
   - What might this indicate?
   - What additional information would you want?
   - What actions would you recommend?
   ```

#### D2. Research Skills
**Information Gathering**:

1. **Vulnerability Research**:
   ```
   Task: Research CVE-2021-44228 (Log4Shell)
   
   Questions:
   - What type of vulnerability is this?
   - What systems are affected?
   - What is the CVSS score?
   - What are the mitigation strategies?
   ```

## 📊 Scoring Guide

### Score Calculation:
- **Section A**: 25 points maximum
- **Section B**: 30 points maximum  
- **Section C**: 25 points maximum
- **Section D**: 20 points maximum
- **Total**: 100 points maximum

### Performance Levels:

#### Beginner (0-40 points)
**Recommendation**: Start with Module 1
**Focus Areas**:
- Basic computer skills strengthening
- Networking fundamentals
- Security awareness training

**Suggested Preparation**:
- CompTIA A+ study materials
- Basic networking course
- Security awareness training

#### Intermediate (41-70 points)
**Recommendation**: Start with Module 1, accelerated pace
**Focus Areas**:
- Hands-on technical skills
- Security tool familiarity
- Practical application

**Suggested Preparation**:
- Network+ study materials
- Basic Linux administration
- Security+ foundations

#### Advanced (71-85 points)
**Recommendation**: Start with Module 2 or 3
**Focus Areas**:
- Specialized security domains
- Advanced tool usage
- Professional development

**Suggested Preparation**:
- Choose specialization track
- Industry certification prep
- Hands-on lab practice

#### Expert (86-100 points)
**Recommendation**: Focus on advanced modules (5-8)
**Focus Areas**:
- Professional specialization
- Leadership skills
- Industry expertise

**Suggested Preparation**:
- Advanced certifications
- Mentoring opportunities
- Industry involvement

## 🎯 Personalized Learning Path

### Based on Assessment Results:

#### Weak in Networking (Section B < 60%)
**Additional Resources**:
- [Professor Messer Network+](https://www.professormesser.com/network-plus/)
- [Cisco Networking Academy](https://www.netacad.com/)
- Hands-on lab: Build home network

#### Weak in Security Concepts (Section C < 60%)
**Additional Resources**:
- [SANS Cyber Aces](https://cyberaces.org/)
- [Cybrary Security+ Course](https://www.cybrary.it/)
- Security awareness training

#### Strong Technical Background (Section D > 80%)
**Accelerated Path**:
- Skip basic modules
- Focus on hands-on labs
- Consider certification track

## 📋 Assessment Instructions

### Before Starting:
1. **Environment Setup**:
   - Quiet workspace
   - Reliable internet connection
   - Note-taking materials
   - 2-3 hours available time

2. **Resources Allowed**:
   - Basic calculator
   - Notepad for calculations
   - No internet research during assessment

3. **Submission Format**:
   - Complete all sections
   - Show work for calculations
   - Explain reasoning for scenario questions

### During Assessment:
- Read questions carefully
- Manage time effectively (30 min per section)
- Skip difficult questions, return later
- Double-check answers before submitting

### After Assessment:
- Review incorrect answers
- Identify knowledge gaps
- Create personalized study plan
- Schedule learning path start date

## 🎓 Next Steps

### Immediate Actions:
1. **Complete Assessment** - Take full assessment honestly
2. **Review Results** - Understand strengths dan weaknesses
3. **Plan Learning Path** - Choose appropriate starting module
4. **Setup Lab Environment** - Prepare technical environment
5. **Schedule Study Time** - Commit to regular learning schedule

### Long-term Planning:
1. **Set Goals** - Define career objectives
2. **Choose Specialization** - Select focus area
3. **Plan Certifications** - Map certification journey
4. **Build Network** - Connect with cybersecurity community
5. **Gain Experience** - Seek hands-on opportunities

---

**🚀 Ready to assess your skills? [Start Assessment](./assessment-form.html)**
