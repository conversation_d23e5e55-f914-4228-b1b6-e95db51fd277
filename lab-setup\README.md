# 🔧 Lab Environment Setup Guide

Panduan lengkap untuk menyiapkan lab environment cybersecurity yang diperlukan untuk menyelesaikan seluruh modul pembelajaran.

## 🖥️ System Requirements

### Minimum Hardware:
- **CPU**: Intel i5 atau AMD Ryzen 5 (4 cores)
- **RAM**: 16GB (32GB sangat direkomendasikan)
- **Storage**: 500GB SSD
- **Network**: Koneksi internet stabil

### Software Requirements:
- **Hypervisor**: VMware Workstation Pro / VirtualBox / Hyper-V
- **Host OS**: Windows 10/11, macOS, atau Linux

## 🔧 Virtual Machines Setup

### 1. Kali Linux (Primary Attack Platform)
```bash
# Download Kali Linux VM
wget https://cdimage.kali.org/kali-2024.1/kali-linux-2024.1-vmware-amd64.7z

# VM Configuration:
- RAM: 4GB minimum (8GB recommended)
- Storage: 80GB
- Network: NAT + Host-Only
```

**Essential Tools Installation:**
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y metasploit-framework burpsuite wireshark nmap
sudo apt install -y python3-pip golang nodejs npm
pip3 install impacket bloodhound
```

### 2. Windows 10/11 (Target System)
```
# Download Windows 10 Evaluation
https://www.microsoft.com/en-us/evalcenter/evaluate-windows-10-enterprise

# VM Configuration:
- RAM: 4GB minimum
- Storage: 60GB
- Network: Host-Only (isolated)
```

**Vulnerable Applications Setup:**
```powershell
# Install vulnerable applications for testing
# DVWA, WebGoat, Metasploitable services
# Disable Windows Defender for lab purposes
Set-MpPreference -DisableRealtimeMonitoring $true
```

### 3. Ubuntu Server (Web Applications)
```bash
# Download Ubuntu Server 22.04 LTS
wget https://releases.ubuntu.com/22.04/ubuntu-22.04.3-live-server-amd64.iso

# VM Configuration:
- RAM: 2GB
- Storage: 40GB
- Network: Host-Only
```

**LAMP Stack Installation:**
```bash
sudo apt update
sudo apt install -y apache2 mysql-server php php-mysql
sudo apt install -y docker.io docker-compose

# Install vulnerable web applications
git clone https://github.com/digininja/DVWA.git /var/www/html/dvwa
git clone https://github.com/WebGoat/WebGoat.git
```

### 4. pfSense (Network Security)
```
# Download pfSense Community Edition
https://www.pfsense.org/download/

# VM Configuration:
- RAM: 1GB
- Storage: 20GB
- Network: 2 NICs (WAN + LAN)
```

### 5. Security Monitoring (ELK Stack)
```bash
# Ubuntu Server with ELK Stack
# VM Configuration:
- RAM: 8GB minimum
- Storage: 100GB
- Network: Host-Only

# ELK Installation
wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo apt-key add -
echo "deb https://artifacts.elastic.co/packages/7.x/apt stable main" | sudo tee /etc/apt/sources.list.d/elastic-7.x.list
sudo apt update
sudo apt install elasticsearch kibana logstash filebeat
```

## 🌐 Network Configuration

### Network Topology:
```
Internet
    |
[pfSense Firewall]
    |
[Internal Network: *************/24]
    |
    ├── Kali Linux (**************)
    ├── Windows Target (**************)
    ├── Ubuntu Server (**************)
    └── ELK Stack (**************)
```

### VMware Network Setup:
1. **Create Custom Networks:**
   - VMnet2: Host-Only (*************/24)
   - VMnet3: Isolated Network

2. **Configure VM Networks:**
   ```
   Kali Linux: VMnet2 + NAT
   Windows: VMnet2 only
   Ubuntu: VMnet2 only
   pfSense: NAT (WAN) + VMnet2 (LAN)
   ELK: VMnet2 only
   ```

## 🔒 Security Considerations

### Lab Isolation:
- ⚠️ **NEVER** connect vulnerable VMs directly to internet
- Use isolated networks for testing
- Snapshot VMs before major changes
- Regular backup of lab environment

### Host Protection:
```bash
# Disable unnecessary services on host
# Enable host firewall
# Use separate user account for lab activities
# Regular antivirus scans
```

## 📋 Verification Checklist

### Network Connectivity:
- [ ] Kali can reach all target systems
- [ ] pfSense routing configured correctly
- [ ] Internet access from Kali only
- [ ] Isolated network for vulnerable systems

### Tools Verification:
```bash
# Test essential tools
nmap -sn *************/24
msfconsole -q -x "version; exit"
burpsuite --version
wireshark --version
```

### Services Status:
- [ ] Apache running on Ubuntu
- [ ] MySQL accessible
- [ ] DVWA accessible via web
- [ ] ELK Stack services running
- [ ] pfSense web interface accessible

## 🚨 Troubleshooting

### Common Issues:

**VM Performance:**
```bash
# Increase VM memory allocation
# Enable hardware acceleration
# Disable unnecessary visual effects
```

**Network Issues:**
```bash
# Check VMware network settings
# Verify IP configurations
# Test connectivity with ping
# Check firewall rules
```

**Tool Installation:**
```bash
# Update package repositories
sudo apt update
# Fix broken dependencies
sudo apt --fix-broken install
# Clear package cache
sudo apt clean
```

## 📚 Additional Resources

- [VMware Workstation Documentation](https://docs.vmware.com/en/VMware-Workstation-Pro/)
- [VirtualBox User Manual](https://www.virtualbox.org/manual/)
- [Kali Linux Documentation](https://www.kali.org/docs/)
- [pfSense Documentation](https://docs.netgate.com/pfsense/)

---

**Next Step**: Once lab setup is complete, proceed to [Module 1: Cybersecurity Fundamentals](../module-01-fundamentals/)
